//Create for Chat.tsx - Employee selection for task assignment
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';

interface Employee {
  id: string;
  name: string;
  email?: string;
  role?: string;
  imageUri?: string;
}

interface EmployeeSelectorProps {
  employees: Employee[];
  onSelect: (employeeId: string) => void;
  placeholder?: string;
}

export default function EmployeeSelector({ employees, onSelect, placeholder }: EmployeeSelectorProps) {
  const themedColors = useThemeColors();
  const { t } = useTranslation();

  const getPlaceholderImage = (role: string) => {
    // Return a default avatar based on role
    switch (role) {
      case 'owner':
        return 'https://via.placeholder.com/50/4CAF50/FFFFFF?text=O';
      case 'admin':
        return 'https://via.placeholder.com/50/2196F3/FFFFFF?text=A';
      case 'caretaker':
        return 'https://via.placeholder.com/50/FF9800/FFFFFF?text=C';
      default:
        return 'https://via.placeholder.com/50/9E9E9E/FFFFFF?text=U';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return '👑';
      case 'admin':
        return '⚙️';
      case 'caretaker':
        return '🧑‍🌾';
      default:
        return '👤';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'owner':
        return t('employees.owner') || 'Owner';
      case 'admin':
        return t('employees.admin') || 'Admin';
      case 'caretaker':
        return t('employees.caretaker') || 'Caretaker';
      default:
        return t('employees.employee') || 'Employee';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: themedColors.surface }]}>
      <Text style={[styles.title, { color: themedColors.text }]}>
        {placeholder || t('employees.selectEmployee') || 'Select Employee'}
      </Text>
      
      {employees.map(employee => (
        <TouchableOpacity
          key={employee.id}
          style={[styles.employeeItem, { borderColor: themedColors.border }]}
          onPress={() => onSelect(employee.id)}
        >
          <View style={styles.employeeContent}>
            <Image 
              source={{ uri: employee.imageUri || getPlaceholderImage(employee.role || 'caretaker') }} 
              style={styles.employeeImage} 
              resizeMode="cover"
            />
            <View style={styles.employeeInfo}>
              <Text style={[styles.employeeName, { color: themedColors.text }]}>
                {employee.name}
              </Text>
              <Text style={[styles.employeeRole, { color: themedColors.textSecondary }]}>
                {getRoleIcon(employee.role || 'caretaker')} {getRoleLabel(employee.role || 'caretaker')}
              </Text>
              {employee.email && (
                <Text style={[styles.employeeEmail, { color: themedColors.textSecondary }]}>
                  {employee.email}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  employeeItem: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    backgroundColor: '#FAFAFA',
  },
  employeeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  employeeImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  employeeRole: {
    fontSize: 14,
    marginBottom: 2,
  },
  employeeEmail: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});
