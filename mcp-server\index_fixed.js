require('dotenv').config(); // Add this at the top
const express = require('express');
const cors = require('cors');
const { OpenAI } = require('openai');
const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Import service modules
const { initializeOpenAI, analyzePromptWithAI, generateAIDietPlan, generateAIHealthPlan } = require('./services/ai-service');
const {
  setFirestore,
  saveAnimalToDatabase,
  saveFarmToDatabase,
  saveExpenseToDatabase,
  saveMilkingToDatabase,
  saveHealthCheckToDatabase,
  savePregnancyToDatabase,
  saveTaskToDatabase,
  formatFarmDescription
} = require('./services/database-service');
const { setStorage, uploadImageToFirebase, uploadBase64ImageToStorage } = require('./services/firebase-storage');
const { validatePregnancyData, findMissingPregnancyInfo, generateMissingInfoMessage } = require('./utils/pregnancyUtils');

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Initialize Firebase Admin
const serviceAccount = require('./firebase-service-account.json'); // You'll need to add this file

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  storageBucket: process.env.STORAGE_BUCKET || 'kissandost-9570f.firebasestorage.app' // Replace with your actual bucket
});

const firestore = admin.firestore();
const storage = admin.storage();

// Initialize service modules with Firebase instances
setFirestore(firestore);
setStorage(storage);

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY // Fixed the environment variable name
});

let previousAnimalData = null;
let previousFarmData = null;

// Helper function to fetch employees for a farm
const getEmployeesForFarm = async (farmId) => {
  try {
    console.log('Fetching employees for farm:', farmId);

    // Get all users who have this farm in their assignedFarmIds
    const usersRef = firestore.collection('users');
    const q = usersRef.where('assignedFarmIds', 'array-contains', farmId);
    const querySnapshot = await q.get();

    const employees = [];
    querySnapshot.forEach((doc) => {
      const userData = doc.data();
      employees.push({
        id: doc.id,
        name: userData.name || 'Unknown',
        email: userData.email || '',
        role: userData.role || 'caretaker',
        imageUri: userData.photo || userData.photoURL || null
      });
    });

    // Sort employees: owner first, then admin, then caretaker
    employees.sort((a, b) => {
      const roleOrder = { 'owner': 0, 'admin': 1, 'caretaker': 2 };
      const aOrder = roleOrder[a.role] ?? 3;
      const bOrder = roleOrder[b.role] ?? 3;

      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // If same role, sort by name
      return a.name.localeCompare(b.name);
    });

    console.log('Found employees:', employees);
    return employees;
  } catch (error) {
    console.error('Error fetching employees for farm:', error);
    return [];
  }
};

// Main chat endpoint
app.post('/chat', async (req, res) => {
  try {
    console.log('=== CHAT REQUEST ===');
    console.log('Request body keys:', Object.keys(req.body));

    const { prompt, imageUri, language = 'en', requestType, userId } = req.body;
    const requestBody = req.body;

    console.log('Request details:', {
      hasPrompt: !!prompt,
      hasImageUri: !!imageUri,
      language,
      requestType,
      userId: userId ? userId.substring(0, 8) + '...' : 'none'
    });

    if (!userId) {
      return res.status(400).json({
        error: 'User ID is required',
        message: language === 'ur' ? 'صارف کی شناخت درکار ہے۔' : 'User ID is required.'
      });
    }

    const messages = [];

    // Handle image analysis with OpenAI Vision
    if (imageUri && !requestType) {
      console.log('Processing image with OpenAI Vision...');

      try {
        const completion = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt || (language === 'ur' ?
                    'اس تصویر میں کیا ہے؟ اگر یہ کوئی جانور ہے تو اس کی نوع، عمر، صحت کی حالت اور دیگر خصوصیات بتائیں۔ اگر یہ کوئی فارم ہے تو اس کی تفصیلات بتائیں۔' :
                    'What do you see in this image? If it\'s an animal, describe its species, age, health condition, and other characteristics. If it\'s a farm, describe its details.')
                },
                {
                  type: 'image_url',
                  image_url: { url: imageUri }
                }
              ]
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        });

        const analysisResult = completion.choices[0]?.message?.content || 'No analysis available';
        console.log('OpenAI Vision analysis:', analysisResult.substring(0, 200) + '...');

        return res.json({
          message: analysisResult,
          imageAnalysis: true
        });
      } catch (visionError) {
        console.error('OpenAI Vision error:', visionError);
        return res.json({
          message: language === 'ur' ?
            'تصویر کا تجزیہ کرنے میں خرابی ہوئی۔ براہ کرم دوبارہ کوشش کریں۔' :
            'Error analyzing image. Please try again.',
          error: true
        });
      }
    }

    // Regular chat functionality
    console.log('Processing regular chat message...');
    const systemMessage = language === 'ur'
      ? 'آپ ایک مددگار اسسٹنٹ ہیں۔ اردو میں جواب دیں۔'
      : 'You are a helpful assistant. Respond in English.';

    messages.push({ role: 'system', content: systemMessage });
    messages.push({ role: 'user', content: prompt });

    console.log('Calling OpenAI for regular chat...');
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages,
    });

    const responseMessage = completion.choices[0]?.message?.content || 'No response generated';
    console.log('Chat response:', responseMessage);

    res.json({
      message: responseMessage,
    });

  } catch (error) {
    console.error('=== SERVER ERROR ===');
    console.error('Error details:', error);
    console.error('Stack trace:', error.stack);

    res.status(500).json({
      error: 'Failed to process chat request',
      details: error.message,
      timestamp: new Date().toISOString(),
      message: req.body.language === 'ur' ?
        'معذرت، کچھ غلط ہوا۔ براہ کرم دوبارہ کوشش کریں۔' :
        'Sorry, something went wrong. Please try again.'
    });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`MCP Server running on http://localhost:${port}`);
  console.log('Firebase Admin initialized');
  console.log('OpenAI client initialized');
});
