// Complete test for task management functionality
require('dotenv').config();

const { analyzePromptWithAI } = require('./services/ai-service');

async function testCompleteTaskFlow() {
  console.log('🧪 Testing Complete Task Management Flow...\n');
  
  try {
    // Test 1: AI Service Task Detection
    console.log('📋 Test 1: AI Service Task Detection');
    const taskPrompts = [
      "Create a task for <PERSON> to clean the milking parlor tomorrow",
      "New task: Fix the fence. High priority.",
      "Assign a task to <PERSON><PERSON> to check the water pump by next Friday"
    ];
    
    for (const prompt of taskPrompts) {
      console.log(`\n🎯 Testing: "${prompt}"`);
      const result = await analyzePromptWithAI(prompt, 'add_task');
      console.log('✅ AI Result:', JSON.stringify(result, null, 2));
    }

    console.log('\n---\n');

    // Test 2: Server Response with Employee Selection
    console.log('📋 Test 2: Server Response with Employee Selection');
    const response = await fetch('http://localhost:3001/open-ai-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "New task: Fix the fence. High priority.",
        language: 'en',
        userId: 'test-user-123',
        userName: 'Test Owner',
        farms: [{
          id: 'test-farm-123',
          name: 'Test Farm',
          location: 'Test Location'
        }]
      }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Server Response:', JSON.stringify(data, null, 2));
      
      if (data.needsEmployeeSelection) {
        console.log('✅ Employee selection flow triggered correctly');
      }
    } else {
      console.log('❌ Server response failed:', response.status);
    }

    console.log('\n---\n');

    // Test 3: Task with Assignee
    console.log('📋 Test 3: Task with Assignee (should show employee not found)');
    const response2 = await fetch('http://localhost:3001/open-ai-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: "Create a task for John to clean the barn",
        language: 'en',
        userId: 'test-user-123',
        userName: 'Test Owner',
        farms: [{
          id: 'test-farm-123',
          name: 'Test Farm',
          location: 'Test Location'
        }]
      }),
    });

    if (response2.ok) {
      const data2 = await response2.json();
      console.log('✅ Server Response 2:', JSON.stringify(data2, null, 2));
    } else {
      console.log('❌ Server response 2 failed:', response2.status);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCompleteTaskFlow();
